[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "babycare-rag"
version = "0.1.0"
description = "A RAG-powered baby care assistant tool for team integration"
readme = "README.md"
requires-python = ">=3.10"
dependencies = [
    "python-dotenv>=1.0.0",
    "faiss-cpu>=1.10.0",
    "openai>=1.0.0",
    "markitdown[all]>=0.1.1",
    "mcp[cli]>=1.6.0",
    "pillow>=11.1.0",
    "rich>=14.0.0",
    "scipy>=1.15.2",
    "tqdm>=4.67.1",
    "requests>=2.31.0",
    "pydantic>=2.0.0",
    "numpy>=1.24.0",
    "llama-index>=0.12.28",
    "streamlit>=1.32.0",
    "boto3>=1.34.0",
    "rank_bm25>=0.2.2",
]

[tool.setuptools.packages.find]
include = ["babycare_rag*"]
