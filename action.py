from typing import Dict, Any, Union
from pydantic import BaseModel
from mcp import ClientSession
import ast

# Optional: import log from agent if shared, else define locally
try:
    from agent import log
except ImportError:
    import datetime
    def log(stage: str, msg: str):
        now = datetime.datetime.now().strftime("%H:%M:%S")
        print(f"[{now}] [{stage}] {msg}")


class ToolCallResult(BaseModel):
    tool_name: str
    arguments: Dict[str, Any]
    result: Union[str, list, dict]
    raw_response: Any
    sources: list[str] = []


def parse_function_call(response: str) -> tuple[str, Dict[str, Any]]:
    """Parses FUNCTION_CALL string into tool name and arguments."""
    try:
        if not response.startswith("FUNCTION_CALL:"):
            raise ValueError("Not a valid FUNCTION_CALL")

        _, function_info = response.split(":", 1)
        parts = [p.strip() for p in function_info.split("|")]
        func_name, param_parts = parts[0], parts[1:]

        result = {}
        for part in param_parts:
            if "=" not in part:
                raise ValueError(f"Invalid param: {part}")
            key, value = part.split("=", 1)

            try:
                parsed_value = ast.literal_eval(value)
            except Exception:
                parsed_value = value.strip()

            # Handle nested keys
            keys = key.split(".")
            current = result
            for k in keys[:-1]:
                current = current.setdefault(k, {})
            current[keys[-1]] = parsed_value

        # log("parser", f"Parsed: {func_name} → {result}")
        return func_name, result

    except Exception as e:
        log("parser", f"❌ Failed to parse FUNCTION_CALL: {e}")
        raise


async def execute_tool(session: ClientSession, tools: list[Any], response: str) -> ToolCallResult:
    """Executes a FUNCTION_CALL via MCP tool session."""
    try:
        tool_name, arguments = parse_function_call(response)

        tool = next((t for t in tools if t.name == tool_name), None)
        if not tool:
            raise ValueError(f"Tool '{tool_name}' not found in registered tools")

        # log("tool", f"⚙️ Calling '{tool_name}' with: {arguments}")
        result = await session.call_tool(tool_name, arguments=arguments)

        if hasattr(result, 'content'):
            # Convert MCP content to Python value
            if isinstance(result.content, list):
                out_items = []
                for item in result.content:
                    text_val = getattr(item, 'text', None)
                    if text_val is not None:
                        out_items.append(text_val)
                    else:
                        out_items.append(str(item))
                out = out_items
            else:
                out = getattr(result.content, 'text', str(result.content))
        else:
            out = str(result)

        # Parse structured JSON from search_documents (first list element)
        sources: list[str] = []
        if tool_name == "search_documents":
            try:
                import json
                payload_str = None
                if isinstance(out, list) and out:
                    payload_str = out[0] if isinstance(out[0], str) else str(out[0])
                elif isinstance(out, str):
                    payload_str = out
                if payload_str:
                    payload = json.loads(payload_str)
                    # normalize minimal shape: {'results':[...], 'sources':[...]} or list
                    if isinstance(payload, dict):
                        sources = list(payload.get('sources', []))
                        out = payload
                    elif isinstance(payload, list):
                        # fall back: list of entries each with {text, context_tag, source, chunk_id}
                        out = {'results': payload, 'sources': []}
                        sources = []
            except Exception as e:
                log("tool", f"search_documents JSON parse failed: {e}")

        log("tool", f"✅ {tool_name} result: {out}")
        return ToolCallResult(
            tool_name=tool_name,
            arguments=arguments,
            result=out,
            raw_response=result,
            sources=sources
        )

    except Exception as e:
        log("tool", f"⚠️ Execution failed for '{response}': {e}")
        raise
